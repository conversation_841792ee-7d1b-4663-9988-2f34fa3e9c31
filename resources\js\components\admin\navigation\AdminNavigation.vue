<template>
  <nav class="flex-1 p-4 overflow-y-auto mobile-scroll">
    <div class="space-y-2">
      <!-- Dashboard -->
      <AdminMenuItem
        :item="dashboardItem"
        :collapsed="adminStore.sidebarCollapsed"
      />

      <!-- Sales Section -->
      <AdminMenuItem
        :item="salesItem"
        :collapsed="adminStore.sidebarCollapsed"
      />

      <!-- Auctions Section -->
      <AdminMenuItem
        :item="auctionsItem"
        :collapsed="adminStore.sidebarCollapsed"
      />

      <!-- Items Section -->
      <AdminMenuItem
        :item="itemsItem"
        :collapsed="adminStore.sidebarCollapsed"
      />

      <!-- Users Section -->
      <AdminMenuItem
        :item="usersItem"
        :collapsed="adminStore.sidebarCollapsed"
      />

      <!-- Financial Section -->
      <AdminMenuItem
        :item="financialItem"
        :collapsed="adminStore.sidebarCollapsed"
      />

      <!-- Reports Section -->
      <AdminMenuItem
        :item="reportsItem"
        :collapsed="adminStore.sidebarCollapsed"
      />

      <!-- Settings Section -->
      <AdminMenuItem
        :item="settingsItem"
        :collapsed="adminStore.sidebarCollapsed"
      />
    </div>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { useAdminStore } from '@/stores/admin';
import AdminMenuItem from './AdminMenuItem.vue';

// Store
const adminStore = useAdminStore();
const route = useRoute();

// Navigation items
const dashboardItem = computed(() => ({
  key: 'dashboard',
  label: 'Dashboard',
  icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z',
  route: '/admin-spa/dashboard',
  active: route.path === '/admin-spa/dashboard' || route.path === '/admin-spa'
}));

const salesItem = computed(() => ({
  key: 'sales',
  label: 'Sales',
  icon: 'M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z',
  route: '/admin-spa/sales',
  // expanded: adminStore.openMenus.sales,
  // children: [
  //   { key: 'sales-overview', label: 'Sales Overview', route: '/admin-spa/sales' },
  //   { key: 'sales-orders', label: 'Orders', route: '/admin-spa/sales/orders' },
  //   { key: 'sales-invoices', label: 'Invoices', route: '/admin-spa/sales/invoices' },
  //   { key: 'sales-customers', label: 'Customers', route: '/admin-spa/sales/customers' },
  //   { key: 'sales-reports', label: 'Sales Reports', route: '/admin-spa/sales/reports' }
  // ]
}));

const auctionsItem = computed(() => ({
  key: 'auctions',
  label: 'Auctions',
  icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10',
  hasChildren: true,
  expanded: adminStore.openMenus.auctions,
  children: [
    { key: 'auctions-bids', label: 'Bid List', route: '/admin-spa/auctions/bids' },
    { key: 'auctions-listings', label: 'Auctions Listings', route: '/admin-spa/auctions/listings' },
    { key: 'auctions-categories', label: 'Auction Categories', route: '/admin-spa/auctions/categories' },
    { key: 'auctions-accounts', label: 'Accounts', route: '/admin-spa/auctions/accounts' },
    { key: 'auctions-suppliers', label: 'Suppliers', route: '/admin-spa/auctions/suppliers' }
  ]
}));

const itemsItem = computed(() => ({
  key: 'items',
  label: 'Items',
  icon: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4',
  hasChildren: true,
  expanded: adminStore.openMenus.items,
  children: [
    { key: 'items-all', label: 'All Items', route: '/admin-spa/items' },
    { key: 'items-add', label: 'Add Item', route: '/admin-spa/items/create' },
    { key: 'items-categories', label: 'Categories', route: '/admin-spa/items/categories' },
    { key: 'items-bulk', label: 'Bulk Import', route: '/admin-spa/items/bulk-import' },
    { key: 'items-conditions', label: 'Item Conditions', route: '/admin-spa/items/conditions' }
  ]
}));

const usersItem = computed(() => ({
  key: 'users',
  label: 'Users',
  icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z',
  hasChildren: true,
  expanded: adminStore.openMenus.users,
  children: [
    { key: 'users-all', label: 'All Users', route: '/admin-spa/users' },
    { key: 'users-bidders', label: 'Bidders', route: '/admin-spa/users/bidders' },
    { key: 'users-sellers', label: 'Sellers', route: '/admin-spa/users/sellers' },
    { key: 'users-admins', label: 'Administrators', route: '/admin-spa/users/administrators' },
    { key: 'users-roles', label: 'User Roles', route: '/admin-spa/users/roles' },
    { key: 'users-permissions', label: 'Permissions', route: '/admin-spa/users/permissions' }
  ]
}));

const financialItem = computed(() => ({
  key: 'financial',
  label: 'Financial',
  icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1',
  hasChildren: true,
  expanded: adminStore.openMenus.financial,
  children: [
    { key: 'financial-transactions', label: 'Transactions', route: '/admin-spa/financial/transactions' },
    { key: 'financial-payments', label: 'Payments', route: '/admin-spa/financial/payments' },
    { key: 'financial-commissions', label: 'Commissions', route: '/admin-spa/financial/commissions' },
    { key: 'financial-invoices', label: 'Invoices', route: '/admin-spa/financial/invoices' },
    { key: 'financial-tax', label: 'Tax Reports', route: '/admin-spa/financial/tax-reports' }
  ]
}));

const reportsItem = computed(() => ({
  key: 'reports',
  label: 'Reports',
  icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
  hasChildren: true,
  expanded: adminStore.openMenus.reports,
  children: [
    { key: 'reports-sales', label: 'Sales Reports', route: '/admin-spa/reports/sales' },
    { key: 'reports-analytics', label: 'User Analytics', route: '/admin-spa/reports/analytics' },
    { key: 'reports-performance', label: 'Performance', route: '/admin-spa/reports/performance' },
    { key: 'reports-custom', label: 'Custom Reports', route: '/admin-spa/reports/custom' }
  ]
}));

const settingsItem = computed(() => ({
  key: 'settings',
  label: 'Settings',
  icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z',
  hasChildren: true,
  expanded: adminStore.openMenus.settings,
  children: [
    { key: 'settings-general', label: 'General', route: '/admin-spa/settings/general' },
    { key: 'settings-auctions', label: 'Auction Settings', route: '/admin-spa/settings/auctions' },
    { key: 'settings-payments', label: 'Payment Gateway', route: '/admin-spa/settings/payments' },
    { key: 'settings-email', label: 'Email Templates', route: '/admin-spa/settings/email' },
    { key: 'settings-logs', label: 'System Logs', route: '/admin-spa/settings/logs' }
  ]
}));
</script>

<style scoped>
/* Mobile optimizations */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}
</style>
